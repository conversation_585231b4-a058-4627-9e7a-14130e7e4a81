/* 基础样式重置 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    line-height: 1.6;
    color: #333333;
    background-color: #F5F5F5;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* 顶部导航栏 */
.header {
    background-color: #000000;
    color: #FFFFFF;
    padding: 16px 0;
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1000;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.header .container {
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.nav-brand {
    display: flex;
    align-items: center;
    gap: 12px;
}

.logo {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    background-color: #FF4D00;
}

.brand-name {
    font-size: 18px;
    font-weight: bold;
    color: #FFFFFF;
}

.nav-menu {
    display: flex;
    gap: 32px;
}

.nav-link {
    color: #FFFFFF;
    text-decoration: none;
    font-weight: 500;
    transition: color 0.3s ease;
}

.nav-link:hover {
    color: #FF4D00;
    text-decoration: underline;
}

.nav-search {
    display: flex;
    align-items: center;
    gap: 8px;
}

.search-input {
    padding: 8px 12px;
    border: 1px solid #EDEDED;
    border-radius: 4px;
    background-color: #FFFFFF;
}

.search-btn {
    background: none;
    border: none;
    color: #FFFFFF;
    cursor: pointer;
    font-size: 16px;
}

/* 产品导航 */
.product-nav {
    background-color: #000000;
    color: #FFFFFF;
    padding: 12px 0;
    margin-top: 70px;
}

.product-nav .container {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.breadcrumb {
    font-size: 14px;
}

.breadcrumb a {
    color: #0066CC;
    text-decoration: none;
}

.product-tabs {
    display: flex;
    gap: 16px;
}

.tab-btn {
    background: none;
    border: none;
    color: #FFFFFF;
    padding: 8px 16px;
    cursor: pointer;
    border-radius: 4px;
    transition: background-color 0.3s ease;
}

.tab-btn.active,
.tab-btn:hover {
    background-color: #FF4D00;
}

/* 主要产品展示 */
.hero-section {
    background-color: #FFFFFF;
    padding: 48px 0;
    margin: 32px 0;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.product-showcase {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 48px;
    align-items: center;
}

.main-product-img {
    width: 100%;
    height: auto;
    border-radius: 6px;
}

.product-info h1 {
    font-size: 32px;
    font-weight: bold;
    color: #000000;
    margin-bottom: 16px;
}

.product-description {
    font-size: 16px;
    color: #333333;
    margin-bottom: 24px;
    line-height: 1.8;
}

.product-highlights {
    display: flex;
    gap: 12px;
}

.highlight-tag {
    background-color: #FF4D00;
    color: #FFFFFF;
    padding: 6px 12px;
    border-radius: 4px;
    font-size: 14px;
    font-weight: 500;
}

/* 特性部分 */
.features-section {
    background-color: #FFFFFF;
    padding: 48px 0;
    margin: 32px 0;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.section-title {
    display: flex;
    align-items: center;
    gap: 12px;
    font-size: 24px;
    font-weight: bold;
    color: #000000;
    margin-bottom: 32px;
}

.icon {
    font-size: 20px;
}

.features-content {
    display: grid;
    gap: 24px;
}

.feature-item h3 {
    font-size: 18px;
    font-weight: 600;
    color: #000000;
    margin-bottom: 12px;
}

.feature-item p {
    color: #333333;
    line-height: 1.8;
}

/* 优势部分 */
.advantages-section {
    background-color: #FFFFFF;
    padding: 48px 0;
    margin: 32px 0;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.advantages-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 48px;
    margin-bottom: 32px;
}

.advantage-img {
    width: 100%;
    height: auto;
    border-radius: 6px;
}

.advantage-list {
    display: flex;
    flex-direction: column;
    gap: 16px;
}

.advantage-item {
    display: flex;
    align-items: flex-start;
    gap: 12px;
}

.bullet {
    color: #FF4D00;
    font-weight: bold;
    margin-top: 2px;
}

.advantage-description h3 {
    font-size: 18px;
    font-weight: 600;
    color: #000000;
    margin-bottom: 12px;
}

.advantage-description p {
    color: #333333;
    line-height: 1.8;
}

/* 应用部分 */
.applications-section {
    background-color: #FFFFFF;
    padding: 48px 0;
    margin: 32px 0;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.application-placeholder {
    text-align: center;
    padding: 48px 0;
    color: #666666;
}

/* 联系表单 */
.contact-section {
    background-color: #FFFFFF;
    padding: 48px 0;
    margin: 32px 0;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.contact-form-wrapper {
    max-width: 600px;
    margin: 0 auto;
    text-align: center;
}

.contact-form-wrapper h3 {
    font-size: 20px;
    font-weight: bold;
    color: #000000;
    margin-bottom: 12px;
}

.contact-form-wrapper p {
    color: #666666;
    margin-bottom: 32px;
}

.contact-form {
    display: flex;
    flex-direction: column;
    gap: 16px;
}

.form-input,
.form-textarea {
    padding: 12px 16px;
    border: 1px solid #EDEDED;
    border-radius: 4px;
    font-size: 14px;
    background-color: #FFFFFF;
}

.form-input:focus,
.form-textarea:focus {
    outline: none;
    border-color: #FF4D00;
}

.submit-btn {
    background-color: #FF4D00;
    color: #FFFFFF;
    border: none;
    padding: 12px 24px;
    border-radius: 4px;
    font-size: 16px;
    font-weight: 500;
    cursor: pointer;
    transition: background-color 0.3s ease;
}

.submit-btn:hover {
    background-color: #E63D00;
}

/* 页脚 */
.footer {
    background-color: #000000;
    color: #FFFFFF;
    padding: 48px 0 24px;
}

.footer-content {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 48px;
    margin-bottom: 32px;
}

.footer-section h4 {
    font-size: 16px;
    font-weight: bold;
    margin-bottom: 12px;
}

.footer-section p {
    color: #CCCCCC;
    font-size: 14px;
}

.footer-bottom {
    border-top: 1px solid #333333;
    padding-top: 24px;
    text-align: center;
}

.footer-brand {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 12px;
}

.footer-logo {
    width: 24px;
    height: 24px;
    border-radius: 50%;
    background-color: #FF4D00;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .header .container {
        flex-direction: column;
        gap: 16px;
    }
    
    .nav-menu {
        gap: 16px;
    }
    
    .product-showcase,
    .advantages-content {
        grid-template-columns: 1fr;
        gap: 24px;
    }
    
    .footer-content {
        grid-template-columns: 1fr;
        gap: 24px;
    }
}
