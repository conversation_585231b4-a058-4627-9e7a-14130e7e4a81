{"design_system_profile": {"design_style": {"color_palette": {"primary": ["#000000", "#FFFFFF"], "secondary": ["#F5F5F5", "#EDEDED"], "accent": ["#FF4D00", "#0066CC"], "usage": {"primary": "Text, headers, navigation background", "secondary": "Background sections, cards, content blocks", "accent": "Buttons, highlights, links, and call-to-actions"}}, "typography": {"fonts": ["Sans-serif system font family"], "hierarchy": {"h1": {"weight": "bold", "size": "large", "color": "#000000"}, "h2": {"weight": "semibold", "size": "medium-large", "color": "#000000"}, "body": {"weight": "regular", "size": "medium", "color": "#333333"}, "links": {"weight": "regular", "size": "medium", "color": "#0066CC", "decoration": "underline on hover"}}}, "spacing": {"padding": "Consistent internal padding inside cards/sections (~16–24px)", "margin": "Vertical separation between sections (~32–48px)", "grid": "Content aligned to centered max-width container with consistent left-right whitespace"}, "visual_hierarchy": {"header": "Fixed top navigation bar with bold brand logo left-aligned", "hero": "Large banner image with minimal overlay text/navigation", "content_sections": "Card-based white backgrounds with drop shadows and rounded corners", "footer": "Dark footer background with grouped navigation links"}}, "structural_elements": {"navigation": {"style": "Horizontal top nav bar", "alignment": "Logo left, menu centered, search/contact right", "hover_states": "Accent color underline/active state highlighting"}, "cards": {"background": "#FFFFFF", "border_radius": "6–8px", "shadow": "Subtle soft shadow", "internal_layout": "Vertical stack: title → body → image or bullet points"}, "buttons": {"primary": {"background": "#FF4D00", "text_color": "#FFFFFF", "border_radius": "4px", "hover": "Slightly darker shade"}, "secondary": {"background": "#FFFFFF", "border": "1px solid #FF4D00", "text_color": "#FF4D00"}}, "images": {"style": "High-quality, full-width or inset into cards", "treatment": "Crisp edges, no overlays except in hero/banner"}, "icons": {"usage": "Section dividers (e.g., 优势, 应用)", "style": "Minimal line icons in grayscale"}}, "layout_principles": {"grid_system": "Single-column centered layout, cards stacked vertically", "alignment": "Left-aligned text inside centered blocks", "responsiveness": "Scalable sections likely collapse into single column for mobile", "sectioning": "Clear separation of hero, features, advantages, applications, contact, footer"}, "other_attributes": {"consistency": "Repetition of card styles across sections for cohesion", "emphasis": "Accent color used sparingly for CTAs and highlights to guide user focus", "readability": "Ample white space, large line height for body text"}}}