# 山百智诚网站说明

根据 `design.json` 设计规范和提供的图片样式生成的企业网站。

## 文件结构

- `index.html` - 主页面文件
- `styles.css` - 样式文件
- `design.json` - 设计规范文件

## 设计特点

### 颜色方案
- 主色调：黑色 (#000000) 和白色 (#FFFFFF)
- 辅助色：浅灰色 (#F5F5F5, #EDEDED)  
- 强调色：橙红色 (#FF4D00) 和蓝色 (#0066CC)

### 布局特点
- 固定顶部导航栏
- 卡片式内容布局，带圆角和阴影
- 响应式设计
- 单列居中布局，最大宽度1200px

### 主要功能区域
1. **顶部导航** - 山百智诚品牌logo、主导航菜单、搜索功能
2. **产品导航** - 面包屑导航和产品标签页
3. **产品展示** - 主要产品图片和介绍，双列布局
4. **特性介绍** - 产品特性详细说明，包含智能控制、节能设计、模块化结构
5. **优势展示** - 图文结合的优势介绍，突出定制化和高品质
6. **应用场景** - 产品应用领域说明
7. **联系表单** - 客户咨询表单，居中布局
8. **页脚信息** - 公司信息和链接，深色背景

## 需要的图片文件

为了完整显示网站，需要添加以下图片文件：

- `logo.png` - 公司logo (建议32x32px，圆形)
- `product-main.jpg` - 主要产品展示图 (建议800x600px)
- `advantage-image.jpg` - 优势展示图片 (建议600x400px)

## 设计规范遵循

严格按照 `design.json` 中的设计规范实现：

### 颜色使用
- 主色调用于文本、标题、导航背景
- 辅助色用于背景区域、卡片、内容块
- 强调色用于按钮、高亮、链接和行动召唤

### 字体层级
- H1: 粗体、大字号、黑色
- H2: 半粗体、中大字号、黑色  
- 正文: 常规字重、中等字号、深灰色
- 链接: 常规字重、蓝色、悬停时下划线

### 间距系统
- 内边距: 卡片/区域内部16-24px
- 外边距: 区域间垂直分隔32-48px
- 网格: 内容居中对齐，左右留白一致

### 视觉层级
- 固定顶部导航栏，品牌logo左对齐
- 大横幅图片，最少覆盖文本/导航
- 基于卡片的白色背景，带投影和圆角
- 深色页脚背景，分组导航链接

## 响应式支持

网站支持移动端适配，在768px以下屏幕会自动调整：
- 导航栏改为垂直布局
- 双列布局改为单列
- 适当调整间距和字体大小
- 保持内容的可读性和可用性

## 技术实现

- 纯HTML/CSS实现，无需JavaScript框架
- 使用CSS Grid和Flexbox进行现代布局
- 遵循Web标准和最佳实践
- 语义化HTML结构
- 良好的可访问性支持
- 优化的性能和加载速度

## 使用说明

1. 将 `index.html` 和 `styles.css` 放在同一目录
2. 添加相应的图片文件到同一目录
3. 在浏览器中打开 `index.html` 查看网站
4. 可根据实际需求修改内容和样式
